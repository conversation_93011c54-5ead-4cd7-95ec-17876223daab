import React, { useState, useEffect, useRef, useCallback } from "react";
import { Radio, Tooltip, Input } from "antd";
import Header from "../Layout/Header";
import {
  Save,
  X,
  Eye,
  Smartphone,
  Tablet,
  Monitor,
  Settings,
  Trash2,
  GripVertical,
  ChevronLeft,
} from "lucide-react";
import useHttp from "../../hooks/use-http";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";
import { DndProvider, useDrag, useDrop } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import PageStructure from "./Component/PageStructure";
import PageSetting from "./Component/PageSetting";
import PagePreview from "./Component/PagePreview";
import { DND_TYPES } from "../../util/content";

const DragDropBuilder = ({ page, onSave, onCancel }) => {
  const api = useHttp();
  const [components, setComponents] = useState([]);
  const [categories, setCategories] = useState([]);
  const [pageData, setPageData] = useState({
    name: "",
    slug: "",
    meta_title: "",
    meta_description: "",
    custom_css: "",
    custom_js: "",
    components: [],
  });

  const [saving, setSaving] = useState(false);
  const [isStructureOpen, setIsStructureOpen] = useState(true);

  useEffect(() => {
    // Fetch components
    api.sendRequest(CONSTANTS.API.components.get, (res) => {
      console.log("Components fetched:", res);
      setComponents(res);
    });

    // Fetch categories
    api.sendRequest(CONSTANTS.API.categories.get, (res) => {
      console.log("Categories fetched:", res);
      setCategories(res);
    });

    if (page) {
      // Ensure existing components have uniqueId for stable React keys
      const componentsWithUniqueId = (page.components || []).map(
        (comp, index) => ({
          ...comp,
          uniqueId:
            comp.uniqueId ||
            `${comp.id}-${index}-${Date.now()}-${Math.random()
              .toString(36)
              .substring(2, 11)}`,
        })
      );

      setPageData({
        name: page.name || "",
        slug: page.slug || "",
        meta_title: page.meta_title || "",
        meta_description: page.meta_description || "",
        custom_css: page.custom_css || "",
        custom_js: page.custom_js || "",
        components: componentsWithUniqueId,
      });
    }
  }, [page]);

  const handleSave = async () => {
    setSaving(true);

    const apiConfig = page
      ? apiGenerator(CONSTANTS.API.pages.update, { id: page.id })
      : CONSTANTS.API.pages.create;

    api.sendRequest(
      apiConfig,
      (res) => {
        console.log("Page saved successfully:", res);
        setSaving(false);
        onSave();
      },
      pageData,
      page ? "Page updated successfully!" : "Page created successfully!",
      (error) => {
        console.error("Error saving page:", error);
        setSaving(false);
      }
    );
  };

  // Reorder helper for immediate component moves
  const moveComponent = (fromIndex, toIndex) => {
    console.log(`Moving component from ${fromIndex} to ${toIndex}`);
    const updatedComponents = [...pageData.components];
    const [movedComponent] = updatedComponents.splice(fromIndex, 1);
    updatedComponents.splice(toIndex, 0, movedComponent);

    setPageData((prevData) => ({
      ...prevData,
      components: updatedComponents,
    }));
  };

  const removeComponentFromPage = (index) => {
    console.log("Removing component at index:", index);
    const updatedComponents = pageData.components.filter((_, i) => i !== index);

    setPageData((prevData) => ({
      ...prevData,
      components: updatedComponents,
    }));
  };

  // Update CSS class for a specific component in page structure
  const handleCssChange = (index, value) => {
    const updated = [...pageData.components];
    updated[index] = { ...updated[index], cssClass: value };

    setPageData((prevData) => ({
      ...prevData,
      components: updated,
    }));
  };

  const groupedComponents = categories.reduce((acc, category) => {
    acc[category.id] = {
      name: category.name,
      color: category.color,
      components: components.filter((comp) => comp.category_id === category.id),
    };
    return acc;
  }, {});

  // Draggable Component Library item - NO debouncing, immediate drag
  const LibraryItem = ({ comp }) => {
    const [{ isDragging }, drag] = useDrag(
      () => ({
        type: DND_TYPES.LIB_ITEM,
        item: () => {
          console.log("Starting library component drag:", comp.name);
          return { component: comp };
        },
        collect: (monitor) => ({ isDragging: monitor.isDragging() }),
        end: (_, monitor) => {
          if (monitor.didDrop()) {
            console.log("Library component dropped successfully:", comp.name);
          } else {
            console.log("Library component drag cancelled:", comp.name);
          }
        },
      }),
      [comp]
    );

    return (
      <div
        ref={drag}
        className="tw-p-3 tw-bg-gray-50 tw-rounded-lg tw-border tw-border-gray-200 tw-cursor-move tw-hover:tw-bg-gray-100 tw-hover:tw-border-gray-300 tw-transition-colors tw-select-none"
        style={{ opacity: isDragging ? 0.6 : 1 }}
      >
        <div className="tw-flex tw-items-center tw-justify-between">
          <div className="tw-flex-1">
            <p className="tw-text-sm tw-font-medium tw-text-gray-900">
              {comp.name}
            </p>
            <p className="tw-text-xs tw-text-gray-500">
              {comp.placeholders ? comp.placeholders.length : 0} placeholders
            </p>
          </div>
          <GripVertical className="tw-w-4 tw-h-4 tw-text-gray-400" />
        </div>
      </div>
    );
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="tw-h-screen tw-flex tw-overflow-hidden">
        {/* Left Sidebar - Components Library */}
        <div className="tw-w-80 tw-bg-white tw-border-r tw-border-gray-200 tw-flex tw-flex-col">
          <div className="tw-p-4 tw-border-b tw-border-gray-200">
            <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900 tw-mb-2">
              Component Library
            </h3>
            <p className="tw-text-xs tw-text-gray-600">
              Drag components to the canvas to build your page
            </p>
          </div>

          <div className="tw-flex-1 tw-overflow-y-auto tw-p-4">
            {Object.entries(groupedComponents).map(
              ([categoryId, categoryData]) => (
                <div key={categoryId} className="tw-mb-6">
                  <div className="tw-flex tw-items-center tw-mb-3">
                    <div
                      className="tw-w-3 tw-h-3 tw-rounded-full tw-mr-2"
                      style={{ backgroundColor: categoryData.color }}
                    />
                    <h4 className="tw-font-medium tw-text-gray-900">
                      {categoryData.name}
                    </h4>
                  </div>

                  <div className="tw-space-y-2">
                    {categoryData.components.map((component) => (
                      <LibraryItem key={component.id} comp={component} />
                    ))}
                  </div>
                </div>
              )
            )}

            {Object.keys(groupedComponents).length === 0 && (
              <div className="tw-text-center tw-py-8">
                <p className="tw-text-gray-500">No components available</p>
                <p className="tw-text-sm tw-text-gray-400 tw-mt-1">
                  Create components first to use them here
                </p>
              </div>
            )}
          </div>
        </div>

        <PagePreview
          pageData={pageData}
          setPageData={setPageData}
          components={components}
          handleSave={handleSave}
          saving={saving}
          onCancel={onCancel}
          isStructureOpen={isStructureOpen}
          setIsStructureOpen={setIsStructureOpen}
        />

        <PageStructure
          isStructureOpen={isStructureOpen}
          setIsStructureOpen={setIsStructureOpen}
          pageData={pageData}
          setPageData={setPageData}
          components={components}
          handleCssChange={handleCssChange}
          removeComponentFromPage={removeComponentFromPage}
          moveComponent={moveComponent}
        />
      </div>
    </DndProvider>
  );
};

export default DragDropBuilder;
