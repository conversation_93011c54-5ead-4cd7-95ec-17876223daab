import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import { Chev<PERSON><PERSON><PERSON><PERSON>, Settings } from "lucide-react";
import { useState, useEffect } from "react";
import { useDrop, useDragLayer } from "react-dnd";
import { DND_TYPES } from "../../../util/content";
import PageSetting from "./PageSetting";
import {
  deviceConfigs,
  generateGlobalPreviewHTML,
} from "../../Components/content";

const PagePreview = ({
  pageData,
  setPageData,
  components,
  handleSave,
  saving,
  onCancel,
  isStructureOpen,
  setIsStructureOpen,
  isComponentOpen,
  setIsComponentOpen,
  // setShowSettings,
}) => {
  const [showSettings, setShowSettings] = useState(false);
  const [previewMode, setPreviewMode] = useState("laptop");
  const [isDragging, setIsDragging] = useState(false);
  const deviceConfig = deviceConfigs(previewMode);

  // Global drag detection
  const { isDraggingGlobal } = useDragLayer((monitor) => ({
    isDraggingGlobal: monitor.isDragging(),
  }));

  // Update local dragging state based on global drag state
  useEffect(() => {
    if (isDraggingGlobal) {
      setIsDragging(true);
    } else {
      // Small delay to allow drop to complete
      const timer = setTimeout(() => setIsDragging(false), 100);
      return () => clearTimeout(timer);
    }
  }, [isDraggingGlobal]);

  const generatePreviewHTML = () => {
    return generateGlobalPreviewHTML({
      type: "page",
      data: pageData.components,
      pageData,
      customCSS: pageData.custom_css,
      customJS: pageData.custom_js,
      components,
      title: pageData?.meta_title || pageData?.name || "Page Preview",
    });
  };
  console.log(pageData, "pageData...", components);
  const getPreviewModeStyles = () => {
    switch (previewMode) {
      case "mobile":
        return "tw-w-80 tw-h-96";
      case "tablet":
        return "tw-w-96 tw-h-96";
      default:
        return "tw-w-full tw-h-96";
    }
  };

  const addComponentToPage = (component) => {
    console.log("Adding component to page:", component);

    const newPageComponent = {
      id: component.id,
      name: component.name,
      order: pageData.components.length,
      cssClass: "",
      uniqueId: `${component.id}-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 11)}`, // Unique ID for React keys
    };

    const updatedComponents = [...pageData.components, newPageComponent];
    console.log("Updated components:", updatedComponents);

    setPageData((prevData) => ({
      ...prevData,
      components: updatedComponents,
    }));
  };

  // Preview Drop Zone Component - Improved for library components (NO debouncing)
  const PreviewDropZone = ({ isDragging, setIsDragging }) => {
    const [{ isOver, canDrop }, drop] = useDrop(
      () => ({
        accept: DND_TYPES.LIB_ITEM,
        drop: (item, monitor) => {
          // Immediate drop without debouncing for library components
          if (monitor.didDrop()) return; // Prevent duplicate drops
          console.log("Dropping component:", item.component);
          addComponentToPage(item.component);
          setIsDragging(false); // Reset dragging state after drop
        },
        collect: (monitor) => ({
          isOver: monitor.isOver({ shallow: true }),
          canDrop: monitor.canDrop(),
        }),
        hover: (_, monitor) => {
          // Set dragging state when hovering
          if (monitor.canDrop()) {
            setIsDragging(true);
          }
        },
      }),
      [pageData.components, setIsDragging] // Dependency on components to ensure fresh state
    );

    const showDropIndicator = isOver && canDrop;

    return (
      <div
        ref={drop}
        className={`tw-absolute tw-inset-0 tw-transition-all tw-duration-150 ${
          isDragging || showDropIndicator ? "tw-z-30" : "tw-z-10"
        } ${
          showDropIndicator
            ? "tw-bg-blue-50/60 tw-border-2 tw-border-dashed tw-border-blue-400"
            : ""
        }`}
        style={{
          minHeight: "400px",
          pointerEvents: "auto", // Always allow pointer events for drop zone
        }}
        onMouseLeave={() => {
          // Reset dragging state when mouse leaves the drop zone
          if (!isOver) {
            setIsDragging(false);
          }
        }}
      >
        {showDropIndicator && (
          <div className="tw-flex tw-items-center tw-justify-center tw-h-full tw-pointer-events-none">
            <div className="tw-bg-white tw-rounded-lg tw-p-6 tw-shadow-xl tw-border-2 tw-border-blue-300 tw-animate-pulse">
              <div className="tw-flex tw-items-center tw-space-x-3">
                <div className="tw-w-3 tw-h-3 tw-bg-blue-500 tw-rounded-full tw-animate-bounce"></div>
                <p className="tw-text-blue-700 tw-font-semibold tw-text-lg">
                  Drop to add component
                </p>
                <div className="tw-w-3 tw-h-3 tw-bg-blue-500 tw-rounded-full tw-animate-bounce tw-animation-delay-100"></div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      {/* Main Canvas Area */}
      <div className="tw-flex-1 tw-flex tw-flex-col">
        {/* Top Toolbar */}
        <div className="tw-flex tw-w-full tw-bg-white tw-border-b tw-border-gray-200 tw-space-x-4">
          {!isComponentOpen && (
            <div className=" tw-flex tw-items-center tw-justify-center tw-border-r tw-border-gray-200">
              <Tooltip
                title={
                  isComponentOpen
                    ? "Hide Page Structure"
                    : "Show Page Structure"
                }
              >
                <button
                  onClick={() => setIsComponentOpen((v) => !v)}
                  className="tw-text-gray-600 tw-px-3 tw-flex tw-items-center tw-justify-center tw-rounded-lg"
                >
                  <ChevronLeft
                    size={30}
                    className={` ${isComponentOpen ? "" : "tw-rotate-180 "}`}
                  />
                </button>
              </Tooltip>
            </div>
          )}
          <div className="tw-p-[21px]  tw-flex tw-w-full tw-items-center tw-justify-between">
            <div className="tw-flex tw-items-center tw-space-x-4">
              <div className="tw-flex tw-rounded-lg  ">
                <div className="tw-flex tw-items-center tw-space-x-1  tw-rounded-lg  tw-mb-1">
                  {Object.entries(deviceConfig)?.map(([key, config]) => (
                    <Tooltip
                      key={key}
                      title={`${config.label} (${config.description})`}
                    >
                      <button
                        type="button"
                        onClick={() => setPreviewMode(key)}
                        className={`tw-flex tw-items-center tw-justify-center tw-w-8 tw-h-8 tw-rounded-md tw-transition-all tw-duration-200 ${
                          previewMode === key
                            ? "tw-bg-[#EAF0FD] tw-text-white tw-shadow-sm"
                            : "tw-text-gray-500 tw-hover:tw-bg-gray-100 tw-hover:tw-text-gray-700"
                        }`}
                      >
                        {config.icon}
                      </button>
                    </Tooltip>
                  ))}
                </div>
              </div>

              <button
                onClick={() => setShowSettings(true)}
                className="tw-flex tw-items-center tw-px-3 tw-py-2 tw-text-gray-600 tw-hover:tw-bg-gray-100 tw-rounded-lg tw-transition-colors"
              >
                <Tooltip title={`Page Settings`}>
                  <Settings className="tw-w-4 tw-h-4 tw-mr-2" />
                </Tooltip>
              </button>
            </div>

            <div className="tw-flex tw-items-center tw-space-x-2">
              <button
                onClick={onCancel}
                className="tw-px-4 tw-py-2 tw-text-gray-600 tw-hover:tw-bg-gray-100 tw-rounded-lg tw-transition-colors"
              >
                Cancel
              </button>

              <Button
                type="primary"
                size="large"
                onClick={handleSave}
                disabled={saving}
                // || !pageData.name || !pageData.slug}
                className="tw-px-6 tw-h-10 tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
              >
                {saving ? (
                  <>
                    <div className="tw-animate-spin tw-rounded-full tw-h-4 tw-w-4 tw-border-b-2 tw-border-white tw-mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>Save</>
                )}
              </Button>

              {/* <button
                onClick={handleSave}
                disabled={saving || !pageData.name || !pageData.slug}
                className="tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-px-4 tw-py-2 tw-rounded-lg tw-font-medium tw-hover:tw-from-blue-700 tw-hover:tw-to-purple-700 tw-transition-all tw-flex tw-items-center tw-disabled:tw-opacity-50"
              >
                {saving ? (
                  <>
                    <div className="tw-animate-spin tw-rounded-full tw-h-4 tw-w-4 tw-border-b-2 tw-border-white tw-mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>Save</>
                )}
              </button> */}
            </div>
          </div>
          {/* Toggle Page Structure inside the same control group */}
          {!isStructureOpen && (
            <div className=" tw-flex tw-items-center tw-justify-center tw-border-l tw-border-gray-200">
              <Tooltip
                title={
                  isStructureOpen
                    ? "Hide Page Structure"
                    : "Show Page Structure"
                }
              >
                <button
                  onClick={() => setIsStructureOpen((v) => !v)}
                  className="tw-text-gray-600 tw-px-3 tw-flex tw-items-center tw-justify-center tw-rounded-lg"
                >
                  <ChevronLeft
                    size={30}
                    className={` ${isStructureOpen ? "" : "tw-rotate-180 "}`}
                  />
                </button>
              </Tooltip>
            </div>
          )}
        </div>

        {/* Canvas */}
        <div className="tw-flex-1 tw-bg-gray-100 tw-p-6 tw-overflow-auto">
          <div className="tw-flex tw-justify-center">
            <div
              className={`tw-bg-white tw-shadow-lg tw-rounded-lg ${getPreviewModeStyles()}`}
              style={{
                width: deviceConfig?.[previewMode]?.width,
                maxWidth: previewMode === "laptop" ? "100%" : "95%",
                height: deviceConfig?.[previewMode]?.height,
                maxHeight:
                  previewMode === "laptop"
                    ? "600px"
                    : deviceConfig[previewMode].height,
              }}
            >
              <div className="tw-relative tw-w-full tw-h-full tw-flex tw-flex-col">
                <iframe
                  srcDoc={generatePreviewHTML()}
                  className={`tw-w-full tw-h-full tw-border-0 ${
                    isDragging ? "tw-z-0" : "tw-z-20"
                  }`}
                  title="Page Preview"
                  style={{
                    pointerEvents: isDragging ? "none" : "auto",
                  }}
                />
                <PreviewDropZone
                  isDragging={isDragging}
                  setIsDragging={setIsDragging}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Page Settings Modal */}
      {showSettings && (
        <PageSetting
          showSettings={showSettings}
          setShowSettings={setShowSettings}
          pageData={pageData}
          setPageData={setPageData}
        />
      )}
    </>
  );
};

export default PagePreview;
